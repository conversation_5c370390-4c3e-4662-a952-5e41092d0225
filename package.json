{"name": "upace-mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "format:fix": "prettier --write . && eslint . --ext .js,.jsx,.ts,.tsx --fix"}, "jest": {"preset": "jest-expo"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "dependencies": {"@expo-google-fonts/dm-sans": "^0.2.3", "@expo/html-elements": "^0.4.2", "@expo/metro-runtime": "~6.1.2", "@expo/vector-icons": "^15.0.2", "@gluestack-ui/accordion": "^1.0.8", "@gluestack-ui/actionsheet": "^0.2.46", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.32", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.8", "@gluestack-ui/checkbox": "^0.1.33", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.22", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.25", "@gluestack-ui/image": "^0.1.11", "@gluestack-ui/input": "^0.1.32", "@gluestack-ui/link": "^0.1.23", "@gluestack-ui/menu": "^0.2.37", "@gluestack-ui/modal": "^0.1.35", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.16", "@gluestack-ui/popover": "^0.1.43", "@gluestack-ui/pressable": "^0.1.17", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.34", "@gluestack-ui/select": "^0.1.30", "@gluestack-ui/slider": "^0.1.26", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.23", "@gluestack-ui/textarea": "^0.1.24", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.37", "@legendapp/motion": "^2.4.0", "@react-native-assets/slider": "^11.0.8", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "@shopify/flash-list": "^2.0.3", "@tanstack/query-sync-storage-persister": "^5.76.1", "@tanstack/react-form": "^1.20.0", "@tanstack/react-query": "^5.89.0", "@tanstack/react-query-persist-client": "^5.76.1", "@tanstack/zod-form-adapter": "^0.42.1", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^4.1.0", "expo": "^54.0.8", "expo-blur": "~15.0.7", "expo-calendar": "~15.0.7", "expo-constants": "~18.0.9", "expo-dev-client": "~6.0.12", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-image": "~3.0.8", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-local-authentication": "~17.0.7", "expo-location": "~19.0.7", "expo-router": "^6.0.6", "expo-secure-store": "~15.0.7", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.7", "expo-video": "~3.0.11", "expo-web-browser": "~15.0.7", "iconsax-react-nativejs": "^0.0.8", "ky": "^1.8.1", "lodash": "^4.17.21", "lucide-react-native": "^0.469.0", "match-sorter": "^8.0.1", "nativewind": "^4.1.23", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-calendars": "^1.1307.0", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.28.0", "react-native-gifted-charts": "^1.4.57", "react-native-keyboard-controller": "1.18.5", "react-native-maps": "1.20.1", "react-native-mmkv": "^3.2.0", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-native-web": "^0.21.0", "react-native-webview": "13.15.0", "react-native-worklets": "0.5.1", "tailwindcss": "^3.4.16", "zod": "^3.25.7", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.30.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.3.3", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.16", "@types/react": "~19.1.10", "@types/react-test-renderer": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "eslint-config-prettier": "10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~54.0.11", "jscodeshift": "^0.15.2", "prettier": "3.6.2", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "18.3.1", "typescript": "~5.9.2", "typescript-eslint": "^8.35.1"}, "resolutions": {"react-native-safe-area-context": "~5.6.0"}, "overrides": {"react-native-safe-area-context": "~5.6.0"}, "private": true}